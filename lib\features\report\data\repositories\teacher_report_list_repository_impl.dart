/// -----
/// teacher_report_list_repository_impl.dart
/// 
/// 教师端报告列表仓库实现
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/features/report/data/datasources/remote/teacher_report_list_remote_data_source.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';
import 'package:flutter_demo/features/report/domain/repositories/teacher_report_list_repository.dart';

/// 教师端报告列表仓库实现
class TeacherReportListRepositoryImpl implements TeacherReportListRepository {
  final TeacherReportListRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;

  TeacherReportListRepositoryImpl(
    this._remoteDataSource,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, StudentReportListResponse>> getTeacherReportList({
    required int pageNum,
    required int pageSize,
    required String planId,
    int? reportDate,
    int? reportEndDate,
    int? reportStartDate,
    int? status,
    required int type,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final response = await _remoteDataSource.getTeacherReportList(
          pageNum: pageNum,
          pageSize: pageSize,
          planId: planId,
          reportDate: reportDate,
          reportEndDate: reportEndDate,
          reportStartDate: reportStartDate,
          status: status,
          type: type,
        );
        return Right(response);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('获取教师端报告列表失败: $e'));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }
}

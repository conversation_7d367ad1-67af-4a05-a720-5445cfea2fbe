/// -----
/// teacher_report_list_bloc.dart
/// 
/// 教师端报告列表BLoC
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/report/domain/usecases/get_teacher_report_list.dart';
import 'package:flutter_demo/features/report/presentation/bloc/teacher_list/teacher_report_list_event.dart';
import 'package:flutter_demo/features/report/presentation/bloc/teacher_list/teacher_report_list_state.dart';

/// 教师端报告列表BLoC
class TeacherReportListBloc extends Bloc<TeacherReportListEvent, TeacherReportListState> {
  final GetTeacherReportList _getTeacherReportList;

  /// 默认每页数量
  static const int _defaultPageSize = 10;

  TeacherReportListBloc(this._getTeacherReportList) : super(TeacherReportListInitial()) {
    on<LoadTeacherReportListEvent>(_onLoadTeacherReportList);
    on<RefreshTeacherReportListEvent>(_onRefreshTeacherReportList);
    on<LoadMoreTeacherReportListEvent>(_onLoadMoreTeacherReportList);
    on<ChangeTeacherReportTypeEvent>(_onChangeTeacherReportType);
    on<ChangeTeacherPlanEvent>(_onChangeTeacherPlan);
  }

  /// 处理加载教师端报告列表事件
  Future<void> _onLoadTeacherReportList(
    LoadTeacherReportListEvent event,
    Emitter<TeacherReportListState> emit,
  ) async {
    emit(TeacherReportListLoading());

    try {
      final result = await _getTeacherReportList(
        GetTeacherReportListParams(
          pageNum: 1,
          pageSize: _defaultPageSize,
          planId: event.planId,
          type: event.type,
          status: event.status,
        ),
      );

      result.fold(
        (failure) => emit(TeacherReportListError(failure.message)),
        (response) {
          if (response.list.isEmpty) {
            emit(TeacherReportListEmpty(
              planId: event.planId,
              type: event.type,
              status: event.status,
            ));
          } else {
            emit(TeacherReportListLoaded(
              reports: response.list,
              planId: event.planId,
              type: event.type,
              status: event.status,
              currentPage: 1,
              pageSize: _defaultPageSize,
              hasMore: response.hasNextPage,
              total: response.total,
            ));
          }
        },
      );
    } on Exception catch (e) {
      emit(TeacherReportListError('加载报告列表失败: $e'));
    }
  }

  /// 处理刷新教师端报告列表事件
  Future<void> _onRefreshTeacherReportList(
    RefreshTeacherReportListEvent event,
    Emitter<TeacherReportListState> emit,
  ) async {
    // 刷新时不显示loading状态，保持当前UI
    final currentState = state;
    if (currentState is TeacherReportListLoaded) {
      emit(currentState.copyWith(isLoadingMore: false));
    }

    try {
      final result = await _getTeacherReportList(
        GetTeacherReportListParams(
          pageNum: 1,
          pageSize: _defaultPageSize,
          planId: event.planId,
          type: event.type,
          status: event.status,
        ),
      );

      result.fold(
        (failure) => emit(TeacherReportListError(failure.message)),
        (response) {
          if (response.list.isEmpty) {
            emit(TeacherReportListEmpty(
              planId: event.planId,
              type: event.type,
              status: event.status,
            ));
          } else {
            emit(TeacherReportListLoaded(
              reports: response.list,
              planId: event.planId,
              type: event.type,
              status: event.status,
              currentPage: 1,
              pageSize: _defaultPageSize,
              hasMore: response.hasNextPage,
              total: response.total,
            ));
          }
        },
      );
    } on Exception catch (e) {
      emit(TeacherReportListError('刷新报告列表失败: $e'));
    }
  }

  /// 处理加载更多教师端报告列表事件
  Future<void> _onLoadMoreTeacherReportList(
    LoadMoreTeacherReportListEvent event,
    Emitter<TeacherReportListState> emit,
  ) async {
    final currentState = state;
    if (currentState is! TeacherReportListLoaded || currentState.isLoadingMore || !currentState.hasMore) {
      return;
    }

    // 设置加载更多状态
    emit(currentState.copyWith(isLoadingMore: true));

    try {
      final nextPage = currentState.currentPage + 1;
      final result = await _getTeacherReportList(
        GetTeacherReportListParams(
          pageNum: nextPage,
          pageSize: _defaultPageSize,
          planId: event.planId,
          type: event.type,
          status: event.status,
        ),
      );

      result.fold(
        (failure) => emit(TeacherReportListError(failure.message)),
        (response) {
          final allReports = [...currentState.reports, ...response.list];
          emit(TeacherReportListLoaded(
            reports: allReports,
            planId: event.planId,
            type: event.type,
            status: event.status,
            currentPage: nextPage,
            pageSize: _defaultPageSize,
            hasMore: response.hasNextPage,
            total: response.total,
          ));
        },
      );
    } on Exception catch (e) {
      emit(TeacherReportListError('加载更多报告失败: $e'));
    }
  }

  /// 处理切换报告类型事件
  Future<void> _onChangeTeacherReportType(
    ChangeTeacherReportTypeEvent event,
    Emitter<TeacherReportListState> emit,
  ) async {
    add(LoadTeacherReportListEvent(
      planId: event.planId,
      type: event.type,
      status: event.status,
    ));
  }

  /// 处理切换实习计划事件
  Future<void> _onChangeTeacherPlan(
    ChangeTeacherPlanEvent event,
    Emitter<TeacherReportListState> emit,
  ) async {
    add(LoadTeacherReportListEvent(
      planId: event.planId,
      type: event.type,
      status: event.status,
    ));
  }
}

/// -----
/// student_report_list_event.dart
/// 
/// 学生报告列表页面的事件定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

abstract class StudentReportListEvent extends Equatable {
  const StudentReportListEvent();

  @override
  List<Object?> get props => [];
}

/// 加载学生报告列表
class LoadStudentReportListEvent extends StudentReportListEvent {
  /// 实习计划ID
  final String planId;
  
  /// 报告类型（0:日报，1:周报，2:月报）
  final int type;
  
  /// 是否重置列表（用于刷新）
  final bool reset;

  const LoadStudentReportListEvent({
    required this.planId,
    required this.type,
    this.reset = false,
  });

  @override
  List<Object?> get props => [planId, type, reset];
}

/// 刷新学生报告列表
class RefreshStudentReportListEvent extends StudentReportListEvent {
  /// 实习计划ID
  final String planId;
  
  /// 报告类型（0:日报，1:周报，2:月报）
  final int type;

  const RefreshStudentReportListEvent({
    required this.planId,
    required this.type,
  });

  @override
  List<Object?> get props => [planId, type];
}

/// 加载更多学生报告列表
class LoadMoreStudentReportListEvent extends StudentReportListEvent {
  /// 实习计划ID
  final String planId;
  
  /// 报告类型（0:日报，1:周报，2:月报）
  final int type;

  const LoadMoreStudentReportListEvent({
    required this.planId,
    required this.type,
  });

  @override
  List<Object?> get props => [planId, type];
}

/// 切换报告类型
class ChangeReportTypeEvent extends StudentReportListEvent {
  /// 实习计划ID
  final String planId;
  
  /// 新的报告类型（0:日报，1:周报，2:月报）
  final int newType;

  const ChangeReportTypeEvent({
    required this.planId,
    required this.newType,
  });

  @override
  List<Object?> get props => [planId, newType];
}

/// 切换实习计划
class ChangePlanEvent extends StudentReportListEvent {
  /// 新的实习计划ID
  final String newPlanId;
  
  /// 当前报告类型（0:日报，1:周报，2:月报）
  final int type;

  const ChangePlanEvent({
    required this.newPlanId,
    required this.type,
  });

  @override
  List<Object?> get props => [newPlanId, type];
}

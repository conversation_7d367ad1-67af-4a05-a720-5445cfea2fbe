/// -----
/// teacher_report_list_event.dart
/// 
/// 教师端报告列表事件
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 教师端报告列表事件基类
abstract class TeacherReportListEvent extends Equatable {
  const TeacherReportListEvent();

  @override
  List<Object?> get props => [];
}

/// 加载教师端报告列表事件
class LoadTeacherReportListEvent extends TeacherReportListEvent {
  /// 实习计划ID
  final String planId;
  
  /// 报告类型（0:日报，1:周报，2:月报）
  final int type;
  
  /// 审批状态 0:待审批，1:已经审批
  final int? status;

  const LoadTeacherReportListEvent({
    required this.planId,
    required this.type,
    this.status,
  });

  @override
  List<Object?> get props => [planId, type, status];
}

/// 刷新教师端报告列表事件
class RefreshTeacherReportListEvent extends TeacherReportListEvent {
  /// 实习计划ID
  final String planId;
  
  /// 报告类型（0:日报，1:周报，2:月报）
  final int type;
  
  /// 审批状态 0:待审批，1:已经审批
  final int? status;

  const RefreshTeacherReportListEvent({
    required this.planId,
    required this.type,
    this.status,
  });

  @override
  List<Object?> get props => [planId, type, status];
}

/// 加载更多教师端报告列表事件
class LoadMoreTeacherReportListEvent extends TeacherReportListEvent {
  /// 实习计划ID
  final String planId;
  
  /// 报告类型（0:日报，1:周报，2:月报）
  final int type;
  
  /// 审批状态 0:待审批，1:已经审批
  final int? status;

  const LoadMoreTeacherReportListEvent({
    required this.planId,
    required this.type,
    this.status,
  });

  @override
  List<Object?> get props => [planId, type, status];
}

/// 切换报告类型事件
class ChangeTeacherReportTypeEvent extends TeacherReportListEvent {
  /// 实习计划ID
  final String planId;
  
  /// 报告类型（0:日报，1:周报，2:月报）
  final int type;
  
  /// 审批状态 0:待审批，1:已经审批
  final int? status;

  const ChangeTeacherReportTypeEvent({
    required this.planId,
    required this.type,
    this.status,
  });

  @override
  List<Object?> get props => [planId, type, status];
}

/// 切换实习计划事件
class ChangeTeacherPlanEvent extends TeacherReportListEvent {
  /// 实习计划ID
  final String planId;
  
  /// 报告类型（0:日报，1:周报，2:月报）
  final int type;
  
  /// 审批状态 0:待审批，1:已经审批
  final int? status;

  const ChangeTeacherPlanEvent({
    required this.planId,
    required this.type,
    this.status,
  });

  @override
  List<Object?> get props => [planId, type, status];
}

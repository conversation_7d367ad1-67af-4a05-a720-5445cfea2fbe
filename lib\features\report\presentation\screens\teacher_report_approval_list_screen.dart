/// -----
/// teacher_report_approval_list_screen.dart
///
/// 教师端报告审核列表页面，用于展示学生提交的日报、周报、月报、总结等报告
/// 支持按待批阅和已批阅进行分类查看
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2023-2024 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/base_report.dart';
import 'package:flutter_demo/features/report/data/models/daily_report.dart';
import 'package:flutter_demo/features/report/data/models/monthly_report.dart';
import 'package:flutter_demo/features/report/data/models/summary_report.dart';
import 'package:flutter_demo/features/report/data/models/weekly_report.dart';
import 'package:flutter_demo/features/report/presentation/screens/teacher_report_approval_detail_screen.dart';
import 'package:flutter_demo/features/report/presentation/widgets/student_info_card.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class TeacherReportApprovalListScreen extends StatefulWidget {
  final ReportType reportType;

  const TeacherReportApprovalListScreen({
    Key? key,
    required this.reportType,
  }) : super(key: key);

  @override
  State<TeacherReportApprovalListScreen> createState() => _TeacherReportApprovalListScreenState();
}

class _TeacherReportApprovalListScreenState extends State<TeacherReportApprovalListScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late String _courseName;
  late List<String> _availableCourses;

  // 待批阅的报告列表
  List<BaseReport> _pendingReports = [];

  // 已批阅的报告列表
  List<BaseReport> _approvedReports = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _courseName = '2021级市场销售2023-2024实习学年第二学期岗位实习';
    _availableCourses = [
      '2021级市场销售2023-2024实习学年第二学期岗位实习',
      '2022级软件工程2023-2024实习学年第一学期岗位实习',
      '2020级电子商务2022-2023实习学年第二学期岗位实习',
    ];

    // 加载数据
    _loadData();

    // 监听标签切换
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 加载报告数据
  void _loadData() {
    // 模拟数据
    final List<BaseReport> allReports = _createMockReports();

    // 分类数据
    _pendingReports = allReports.where((report) => report.status == ReportStatus.submitted).toList();
    _approvedReports = allReports.where((report) =>
      report.status == ReportStatus.approved || report.status == ReportStatus.rejected
    ).toList();
  }

  // 创建模拟报告数据
  List<BaseReport> _createMockReports() {
    List<BaseReport> reports = [];

    // 根据不同的报告类型创建不同的模拟数据
    for (int i = 0; i < 10; i++) {
      switch (widget.reportType) {
        case ReportType.daily:
          reports.add(DailyReport(
            id: 'report_$i',
            userId: 'user_$i',
            userName: ['李成刚', '陈诚', '张洪涛'][i % 3],
            courseName: _courseName,
            createdAt: DateTime.now().subtract(Duration(days: i)),
            status: i < 5 ? ReportStatus.submitted : (i % 2 == 0 ? ReportStatus.approved : ReportStatus.rejected),
            reportDate: DateTime.now().subtract(Duration(days: i)),
            workContent: '已完成用户个人中心UI优化，已全量上线；\n修复订单状态同步延迟BUG，测试通过率100%',
            learningContent: '学习了Flutter状态管理',
            problems: '已完成用户个人中心UI优化，已全量上线？',
            plans: '计划完成剩余功能开发',
            teacherComment: i >= 5 ? '写的不错，但仍然有改进的空间，可以把日报写的更加详细点' : null,
            teacherName: i >= 5 ? '王老师' : null,
            commentTime: i >= 5 ? DateTime.now() : null,
          ));
          break;
        case ReportType.weekly:
          reports.add(WeeklyReport(
            id: 'report_$i',
            userId: 'user_$i',
            userName: ['李成刚', '陈诚', '张洪涛'][i % 3],
            courseName: _courseName,
            createdAt: DateTime.now().subtract(Duration(days: i * 7)),
            status: i < 5 ? ReportStatus.submitted : (i % 2 == 0 ? ReportStatus.approved : ReportStatus.rejected),
            startDate: DateTime.now().subtract(Duration(days: i * 7 + 7)),
            endDate: DateTime.now().subtract(Duration(days: i * 7)),
            weekSummary: '已完成用户个人中心UI优化，已全量上线；\n修复订单状态同步延迟BUG，测试通过率100%',
            achievements: '成功上线新功能',
            problems: '已完成用户个人中心UI优化，已全量上线？',
            nextWeekPlan: '计划完成剩余功能开发',
            teacherComment: i >= 5 ? '写的不错，但仍然有改进的空间，可以把周报写的更加详细点' : null,
            teacherName: i >= 5 ? '王老师' : null,
            commentTime: i >= 5 ? DateTime.now() : null,
          ));
          break;
        case ReportType.monthly:
          reports.add(MonthlyReport(
            id: 'report_$i',
            userId: 'user_$i',
            userName: ['李成刚', '陈诚', '张洪涛'][i % 3],
            courseName: _courseName,
            createdAt: DateTime.now().subtract(Duration(days: i * 30)),
            status: i < 5 ? ReportStatus.submitted : (i % 2 == 0 ? ReportStatus.approved : ReportStatus.rejected),
            monthSummary: '已完成用户个人中心UI优化，已全量上线；\n修复订单状态同步延迟BUG，测试通过率100%',
            achievements: '成功上线新功能',
            problems: '已完成用户个人中心UI优化，已全量上线？',
            nextMonthPlan: '计划完成剩余功能开发',
            suggestions: '建议加强代码审核',
            teacherComment: i >= 5 ? '写的不错，但仍然有改进的空间，可以把月报写的更加详细点' : null,
            teacherName: i >= 5 ? '王老师' : null,
            commentTime: i >= 5 ? DateTime.now() : null,
          ));
          break;
        case ReportType.summary:
          reports.add(SummaryReport(
            id: 'report_$i',
            userId: 'user_$i',
            userName: ['李成刚', '陈诚', '张洪涛'][i % 3],
            courseName: _courseName,
            createdAt: DateTime.now().subtract(Duration(days: i * 30)),
            status: i < 5 ? ReportStatus.submitted : (i % 2 == 0 ? ReportStatus.approved : ReportStatus.rejected),
            summary: '已完成用户个人中心UI优化，已全量上线；\n修复订单状态同步延迟BUG，测试通过率100%',
            achievements: '成功上线新功能',
            experience: '学到了很多实用技能',
            problems: '已完成用户个人中心UI优化，已全量上线？',
            suggestions: '建议加强代码审核',
            teacherComment: i >= 5 ? '写的不错，但仍然有改进的空间，可以把总结写的更加详细点' : null,
            teacherName: i >= 5 ? '王老师' : null,
            commentTime: i >= 5 ? DateTime.now() : null,
          ));
          break;
      }
    }

    return reports;
  }

  // 获取报告类型的标题和描述
  Map<String, String> _getReportTypeInfo() {
    switch (widget.reportType) {
      case ReportType.daily:
        return {
          'title': '日报审核',
          'subtitle': '学生日报审核管理',
          'description': '查看和批阅学生提交的日报',
        };
      case ReportType.weekly:
        return {
          'title': '周报审核',
          'subtitle': '学生周报审核管理',
          'description': '查看和批阅学生提交的周报',
        };
      case ReportType.monthly:
        return {
          'title': '月报审核',
          'subtitle': '学生月报审核管理',
          'description': '查看和批阅学生提交的月报',
        };
      case ReportType.summary:
        return {
          'title': '总结审核',
          'subtitle': '学生总结审核管理',
          'description': '查看和批阅学生提交的实习总结',
        };
    }
  }

  @override
  Widget build(BuildContext context) {
    final reportTypeInfo = _getReportTypeInfo();

    return Scaffold(
      backgroundColor: Colors.grey[100],
      // 使用透明AppBar，因为TopBannerSection会延伸到状态栏
      appBar: CustomAppBar(
        title: reportTypeInfo['title'] ?? '',
        backgroundColor: Colors.transparent,
        elevation: 0,
        showBackButton: true,
      ),
      body: Column(
        children: [


          // 课程头部 - 自动从全局状态获取实习计划数据
          const CourseHeaderSection(),

          // 标签栏
          ApprovalTabBar(
            controller: _tabController,
            pendingCount: _pendingReports.length,
            pendingText: '待批阅',
            approvedText: '已批阅',
          ),

          // 页面内容
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildReportList(_pendingReports, isPending: true),
                _buildReportList(_approvedReports, isPending: false),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建报告列表
  Widget _buildReportList(List<BaseReport> reports, {required bool isPending}) {
    if (reports.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              isPending ? '暂无待批阅的${widget.reportType.displayName}' : '暂无已批阅的${widget.reportType.displayName}',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: reports.length,
      itemBuilder: (context, index) {
        final report = reports[index];
        return _buildReportItem(report, isPending: isPending);
      },
    );
  }

  // 构建报告项
  Widget _buildReportItem(BaseReport report, {required bool isPending}) {
    // 获取报告周期信息
    String periodInfo = _getReportPeriodInfo(report);

    // 获取报告内容摘要
    String contentSummary = _getReportContentSummary(report);
    String contentAnswer = _getReportContentAnswer(report);

    // 获取状态文本和颜色
    String statusText = isPending ? '待批阅' : (report.status == ReportStatus.approved ? '已批阅' : '已驳回');
    Color statusColor = isPending ? AppTheme.blue2165f6 : (report.status == ReportStatus.approved ? AppTheme.black999 : Colors.red);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: () {
          // 跳转到报告详情页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TeacherReportApprovalDetailScreen(
                report: report,
                reportType: widget.reportType,
              ),
            ),
          ).then((value) {
            // 如果返回值为true，表示评阅已提交，需要刷新列表
            if (value == true) {
              _loadData();
            }
          });
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 学生信息卡片
            StudentInfoCard(
              userName: report.userName,
              company: '武汉谦通信息技术有限公司',
              status: report.status,
              position: '程序员',
              avatarRadius: 88.r,
              margin: EdgeInsets.zero,
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            ),

            // 内容部分
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 周期信息
                  Text(
                    periodInfo,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 问题和回答
                  // 回答内容 - 使用灰色背景
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        contentSummary,
                        style: TextStyle(
                          fontSize: 24.sp,
                          color: AppTheme.black333,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        contentAnswer,
                        style: TextStyle(
                          fontSize: 24.sp,
                          color: AppTheme.black333,
                        ),
                      )
                    ],
                  ),
                ),

                  const SizedBox(height: 16),

                  // 提交时间和查看按钮
                  Row(
                    children: [
                      Text(
                        '提交时间：${DateFormat('yyyy-MM-dd HH:mm').format(report.createdAt)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      const Spacer(),
                      // 查看按钮
                      Row(
                        children: [
                          Text(
                            '查看',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          Icon(
                            Icons.chevron_right,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 获取报告周期信息
  String _getReportPeriodInfo(BaseReport report) {
    switch (widget.reportType) {
      case ReportType.daily:
        final dailyReport = report as DailyReport;
        return DateFormat('yyyy.MM.dd').format(dailyReport.reportDate);
      case ReportType.weekly:
        final weeklyReport = report as WeeklyReport;
        return '第三周周报 ${DateFormat('yyyy.MM.dd').format(weeklyReport.startDate)}—${DateFormat('yyyy.MM.dd').format(weeklyReport.endDate)}';
      case ReportType.monthly:
        return '第三月月报 2025.05.15—2025.05.20';
      case ReportType.summary:
        return '实习总结 2025.05.15—2025.05.20';
    }
  }

  // 获取报告内容摘要 - 问题部分
  String _getReportContentSummary(BaseReport report) {
    switch (widget.reportType) {
      case ReportType.daily:
        final dailyReport = report as DailyReport;
        return dailyReport.problems;
      case ReportType.weekly:
        final weeklyReport = report as WeeklyReport;
        return weeklyReport.problems;
      case ReportType.monthly:
        final monthlyReport = report as MonthlyReport;
        return monthlyReport.problems;
      case ReportType.summary:
        final summaryReport = report as SummaryReport;
        return summaryReport.problems;
    }
  }

  // 获取报告内容摘要 - 回答部分
  String _getReportContentAnswer(BaseReport report) {
    switch (widget.reportType) {
      case ReportType.daily:
        final dailyReport = report as DailyReport;
        return dailyReport.workContent;
      case ReportType.weekly:
        final weeklyReport = report as WeeklyReport;
        return weeklyReport.weekSummary;
      case ReportType.monthly:
        final monthlyReport = report as MonthlyReport;
        return monthlyReport.monthSummary;
      case ReportType.summary:
        final summaryReport = report as SummaryReport;
        return summaryReport.summary;
    }
  }
}
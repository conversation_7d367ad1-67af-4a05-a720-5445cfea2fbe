/// -----
/// teacher_report_approval_list_screen.dart
///
/// 教师端报告审核列表页面，用于展示学生提交的日报、周报、月报、总结等报告
/// 支持按待批阅和已批阅进行分类查看
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2023-2024 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';
import 'package:flutter_demo/features/report/presentation/bloc/teacher_list/teacher_report_list_bloc.dart';
import 'package:flutter_demo/features/report/presentation/bloc/teacher_list/teacher_report_list_event.dart';
import 'package:flutter_demo/features/report/presentation/bloc/teacher_list/teacher_report_list_state.dart';

import 'package:flutter_demo/features/report/presentation/widgets/student_info_card.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class TeacherReportApprovalListScreen extends StatefulWidget {
  final ReportType reportType;

  const TeacherReportApprovalListScreen({
    Key? key,
    required this.reportType,
  }) : super(key: key);

  @override
  State<TeacherReportApprovalListScreen> createState() => _TeacherReportApprovalListScreenState();
}

class _TeacherReportApprovalListScreenState extends State<TeacherReportApprovalListScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late TeacherReportListBloc _pendingBloc;
  late TeacherReportListBloc _approvedBloc;
  late ScrollController _pendingScrollController;
  late ScrollController _approvedScrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 初始化BLoC
    _pendingBloc = GetIt.instance<TeacherReportListBloc>();
    _approvedBloc = GetIt.instance<TeacherReportListBloc>();

    // 初始化滚动控制器
    _pendingScrollController = ScrollController();
    _approvedScrollController = ScrollController();

    // 添加滚动监听器用于分页加载
    _pendingScrollController.addListener(_onPendingScroll);
    _approvedScrollController.addListener(_onApprovedScroll);

    // 加载数据
    _loadInitialData();

    // 监听标签切换
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pendingBloc.close();
    _approvedBloc.close();
    _pendingScrollController.dispose();
    _approvedScrollController.dispose();
    super.dispose();
  }

  /// 加载初始数据
  void _loadInitialData() {
    final planListBloc = GetIt.instance<PlanListGlobalBloc>();
    final planState = planListBloc.state;

    if (planState is PlanListGlobalLoadedState && planState.currentPlanId != null) {
      // 加载待批阅数据
      _pendingBloc.add(LoadTeacherReportListEvent(
        planId: planState.currentPlanId!,
        type: _getReportTypeValue(widget.reportType),
        status: 0, // 待审批
      ));

      // 加载已批阅数据
      _approvedBloc.add(LoadTeacherReportListEvent(
        planId: planState.currentPlanId!,
        type: _getReportTypeValue(widget.reportType),
        status: 1, // 已审批
      ));
    }
  }

  /// 滚动监听，实现分页加载 - 待批阅列表
  void _onPendingScroll() {
    if (_pendingScrollController.position.pixels >= _pendingScrollController.position.maxScrollExtent - 200) {
      final state = _pendingBloc.state;
      if (state is TeacherReportListLoaded && state.hasMore && !state.isLoadingMore) {
        _pendingBloc.add(LoadMoreTeacherReportListEvent(
          planId: state.planId,
          type: state.type,
          status: state.status,
        ));
      }
    }
  }

  /// 滚动监听，实现分页加载 - 已批阅列表
  void _onApprovedScroll() {
    if (_approvedScrollController.position.pixels >= _approvedScrollController.position.maxScrollExtent - 200) {
      final state = _approvedBloc.state;
      if (state is TeacherReportListLoaded && state.hasMore && !state.isLoadingMore) {
        _approvedBloc.add(LoadMoreTeacherReportListEvent(
          planId: state.planId,
          type: state.type,
          status: state.status,
        ));
      }
    }
  }

  /// 将ReportType转换为API需要的数值
  int _getReportTypeValue(ReportType type) {
    switch (type) {
      case ReportType.daily:
        return 0;
      case ReportType.weekly:
        return 1;
      case ReportType.monthly:
        return 2;
      case ReportType.summary:
        return 3;
    }
  }



  // 获取报告类型的标题和描述
  Map<String, String> _getReportTypeInfo() {
    switch (widget.reportType) {
      case ReportType.daily:
        return {
          'title': '日报审核',
          'subtitle': '学生日报审核管理',
          'description': '查看和批阅学生提交的日报',
        };
      case ReportType.weekly:
        return {
          'title': '周报审核',
          'subtitle': '学生周报审核管理',
          'description': '查看和批阅学生提交的周报',
        };
      case ReportType.monthly:
        return {
          'title': '月报审核',
          'subtitle': '学生月报审核管理',
          'description': '查看和批阅学生提交的月报',
        };
      case ReportType.summary:
        return {
          'title': '总结审核',
          'subtitle': '学生总结审核管理',
          'description': '查看和批阅学生提交的实习总结',
        };
    }
  }

  @override
  Widget build(BuildContext context) {
    final reportTypeInfo = _getReportTypeInfo();

    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: _pendingBloc),
        BlocProvider.value(value: _approvedBloc),
      ],
      child: Scaffold(
        backgroundColor: Colors.grey[100],
        // 使用透明AppBar，因为TopBannerSection会延伸到状态栏
        appBar: CustomAppBar(
          title: reportTypeInfo['title'] ?? '',
          backgroundColor: Colors.transparent,
          elevation: 0,
          showBackButton: true,
        ),
        body: Column(
          children: [
            // 课程头部 - 自动从全局状态获取实习计划数据
            const CourseHeaderSection(),

            // 标签栏
            BlocBuilder<TeacherReportListBloc, TeacherReportListState>(
              bloc: _pendingBloc,
              builder: (context, pendingState) {
                int pendingCount = 0;
                if (pendingState is TeacherReportListLoaded) {
                  pendingCount = pendingState.reports.length;
                }

                return ApprovalTabBar(
                  controller: _tabController,
                  pendingCount: pendingCount,
                  pendingText: '待批阅',
                  approvedText: '已批阅',
                );
              },
            ),

            // 页面内容
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPendingReportList(),
                  _buildApprovedReportList(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建待批阅报告列表
  Widget _buildPendingReportList() {
    return BlocBuilder<TeacherReportListBloc, TeacherReportListState>(
      bloc: _pendingBloc,
      builder: (context, state) {
        if (state is TeacherReportListLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is TeacherReportListError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(state.message, style: TextStyle(color: Colors.grey[600])),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadInitialData,
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        } else if (state is TeacherReportListEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.assignment_outlined, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  '暂无待批阅的${widget.reportType.displayName}',
                  style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                ),
              ],
            ),
          );
        } else if (state is TeacherReportListLoaded) {
          return RefreshIndicator(
            onRefresh: () async {
              final planListBloc = GetIt.instance<PlanListGlobalBloc>();
              final planState = planListBloc.state;
              if (planState is PlanListGlobalLoadedState && planState.currentPlanId != null) {
                _pendingBloc.add(RefreshTeacherReportListEvent(
                  planId: planState.currentPlanId!,
                  type: _getReportTypeValue(widget.reportType),
                  status: 0,
                ));
              }
            },
            child: ListView.builder(
              controller: _pendingScrollController,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              itemCount: state.reports.length + (state.isLoadingMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == state.reports.length) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }
                final report = state.reports[index];
                return _buildReportItem(report, isPending: true);
              },
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  /// 构建已批阅报告列表
  Widget _buildApprovedReportList() {
    return BlocBuilder<TeacherReportListBloc, TeacherReportListState>(
      bloc: _approvedBloc,
      builder: (context, state) {
        if (state is TeacherReportListLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is TeacherReportListError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(state.message, style: TextStyle(color: Colors.grey[600])),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadInitialData,
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        } else if (state is TeacherReportListEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.assignment_outlined, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  '暂无已批阅的${widget.reportType.displayName}',
                  style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                ),
              ],
            ),
          );
        } else if (state is TeacherReportListLoaded) {
          return RefreshIndicator(
            onRefresh: () async {
              final planListBloc = GetIt.instance<PlanListGlobalBloc>();
              final planState = planListBloc.state;
              if (planState is PlanListGlobalLoadedState && planState.currentPlanId != null) {
                _approvedBloc.add(RefreshTeacherReportListEvent(
                  planId: planState.currentPlanId!,
                  type: _getReportTypeValue(widget.reportType),
                  status: 1,
                ));
              }
            },
            child: ListView.builder(
              controller: _approvedScrollController,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              itemCount: state.reports.length + (state.isLoadingMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == state.reports.length) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }
                final report = state.reports[index];
                return _buildReportItem(report, isPending: false);
              },
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  /// 构建报告项
  Widget _buildReportItem(StudentReportListItem report, {required bool isPending}) {
    // 获取报告周期信息
    String periodInfo = _getReportPeriodInfo(report);

    // 获取报告内容摘要
    String contentSummary = _getReportContentSummary(report);
    String contentAnswer = _getReportContentAnswer(report);

    // 获取状态文本和颜色
    String statusText = isPending ? '待批阅' : (report.status == 1 ? '已批阅' : '已驳回');
    Color statusColor = isPending ? AppTheme.blue2165f6 : (report.status == 1 ? AppTheme.black999 : Colors.red);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: () {
          // 暂时禁用详情页面跳转，等待详情页面适配新的数据结构
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 学生信息卡片
            StudentInfoCard(
              userName: report.studentName,
              company: report.companyName,
              status: report.status == 0 ? ReportStatus.submitted : ReportStatus.approved,
              position: report.jobName ?? '程序员',
              avatarRadius: 88.r,
              margin: EdgeInsets.zero,
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            ),

            // 内容部分
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 周期信息
                  Text(
                    periodInfo,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 问题和回答
                  // 回答内容 - 使用灰色背景
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          contentSummary,
                          style: TextStyle(
                            fontSize: 24.sp,
                            color: AppTheme.black333,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          contentAnswer,
                          style: TextStyle(
                            fontSize: 24.sp,
                            color: AppTheme.black333,
                          ),
                        )
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 提交时间和查看按钮
                  Row(
                    children: [
                      Text(
                        '提交时间：${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.fromMillisecondsSinceEpoch(report.createTime))}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      const Spacer(),
                      // 查看按钮
                      Row(
                        children: [
                          Text(
                            '查看',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          Icon(
                            Icons.chevron_right,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取报告周期信息
  String _getReportPeriodInfo(StudentReportListItem report) {
    switch (widget.reportType) {
      case ReportType.daily:
        return DateFormat('yyyy.MM.dd').format(DateTime.fromMillisecondsSinceEpoch(report.reportDate));
      case ReportType.weekly:
        final startDate = report.reportStartDate != null
            ? DateTime.fromMillisecondsSinceEpoch(report.reportStartDate!)
            : DateTime.fromMillisecondsSinceEpoch(report.reportDate);
        final endDate = report.reportEndDate != null
            ? DateTime.fromMillisecondsSinceEpoch(report.reportEndDate!)
            : DateTime.fromMillisecondsSinceEpoch(report.reportDate);
        return '第三周周报 ${DateFormat('yyyy.MM.dd').format(startDate)}—${DateFormat('yyyy.MM.dd').format(endDate)}';
      case ReportType.monthly:
        final startDate = report.reportStartDate != null
            ? DateTime.fromMillisecondsSinceEpoch(report.reportStartDate!)
            : DateTime.fromMillisecondsSinceEpoch(report.reportDate);
        final endDate = report.reportEndDate != null
            ? DateTime.fromMillisecondsSinceEpoch(report.reportEndDate!)
            : DateTime.fromMillisecondsSinceEpoch(report.reportDate);
        return '第三月月报 ${DateFormat('yyyy.MM.dd').format(startDate)}—${DateFormat('yyyy.MM.dd').format(endDate)}';
      case ReportType.summary:
        final startDate = report.reportStartDate != null
            ? DateTime.fromMillisecondsSinceEpoch(report.reportStartDate!)
            : DateTime.fromMillisecondsSinceEpoch(report.reportDate);
        final endDate = report.reportEndDate != null
            ? DateTime.fromMillisecondsSinceEpoch(report.reportEndDate!)
            : DateTime.fromMillisecondsSinceEpoch(report.reportDate);
        return '实习总结 ${DateFormat('yyyy.MM.dd').format(startDate)}—${DateFormat('yyyy.MM.dd').format(endDate)}';
    }
  }

  /// 获取报告内容摘要 - 问题部分
  String _getReportContentSummary(StudentReportListItem report) {
    // 从reportContent中提取问题部分，这里简化处理，直接显示部分内容
    final content = report.reportContent;
    if (content.length > 50) {
      return '${content.substring(0, 50)}...';
    }
    return content;
  }

  /// 获取报告内容摘要 - 回答部分
  String _getReportContentAnswer(StudentReportListItem report) {
    // 从reportContent中提取回答部分，这里简化处理，直接显示部分内容
    final content = report.reportContent;
    if (content.length > 100) {
      return content.substring(0, 100);
    }
    return content;
  }
}
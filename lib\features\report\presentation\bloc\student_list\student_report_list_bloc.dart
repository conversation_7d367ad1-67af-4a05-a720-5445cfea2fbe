/// -----
/// student_report_list_bloc.dart
/// 
/// 学生报告列表页面的BLoC
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/report/domain/usecases/get_student_report_list.dart';
import 'package:flutter_demo/features/report/presentation/bloc/student_list/student_report_list_event.dart';
import 'package:flutter_demo/features/report/presentation/bloc/student_list/student_report_list_state.dart';

/// 学生报告列表BLoC
class StudentReportListBloc extends Bloc<StudentReportListEvent, StudentReportListState> {
  final GetStudentReportList _getStudentReportList;

  /// 默认每页数量
  static const int _defaultPageSize = 10;

  StudentReportListBloc(this._getStudentReportList) : super(StudentReportListInitial()) {
    on<LoadStudentReportListEvent>(_onLoadStudentReportList);
    on<RefreshStudentReportListEvent>(_onRefreshStudentReportList);
    on<LoadMoreStudentReportListEvent>(_onLoadMoreStudentReportList);
    on<ChangeReportTypeEvent>(_onChangeReportType);
    on<ChangePlanEvent>(_onChangePlan);
  }

  /// 处理加载学生报告列表事件
  Future<void> _onLoadStudentReportList(
    LoadStudentReportListEvent event,
    Emitter<StudentReportListState> emit,
  ) async {
    // 如果是重置加载或者当前状态不是已加载状态，显示loading
    if (event.reset || state is! StudentReportListLoaded) {
      emit(StudentReportListLoading());
    }

    try {
      final result = await _getStudentReportList(
        GetStudentReportListParams(
          pageNum: 1,
          pageSize: _defaultPageSize,
          planId: event.planId,
          type: event.type,
        ),
      );

      result.fold(
        (failure) => emit(StudentReportListError(failure.message)),
        (response) {
          if (response.list.isEmpty) {
            emit(StudentReportListEmpty(
              planId: event.planId,
              type: event.type,
            ));
          } else {
            emit(StudentReportListLoaded(
              reports: response.list,
              planId: event.planId,
              type: event.type,
              currentPage: 1,
              pageSize: _defaultPageSize,
              hasMore: response.hasNextPage,
              total: response.total,
            ));
          }
        },
      );
    } on Exception catch (e) {
      emit(StudentReportListError('加载报告列表失败: $e'));
    }
  }

  /// 处理刷新学生报告列表事件
  Future<void> _onRefreshStudentReportList(
    RefreshStudentReportListEvent event,
    Emitter<StudentReportListState> emit,
  ) async {
    if (state is StudentReportListLoaded) {
      final currentState = state as StudentReportListLoaded;
      emit(StudentReportListRefreshing(
        reports: currentState.reports,
        planId: event.planId,
        type: event.type,
        currentPage: currentState.currentPage,
        pageSize: currentState.pageSize,
        hasMore: currentState.hasMore,
        total: currentState.total,
      ));
    }

    try {
      final result = await _getStudentReportList(
        GetStudentReportListParams(
          pageNum: 1,
          pageSize: _defaultPageSize,
          planId: event.planId,
          type: event.type,
        ),
      );

      result.fold(
        (failure) => emit(StudentReportListError(failure.message)),
        (response) {
          if (response.list.isEmpty) {
            emit(StudentReportListEmpty(
              planId: event.planId,
              type: event.type,
            ));
          } else {
            emit(StudentReportListLoaded(
              reports: response.list,
              planId: event.planId,
              type: event.type,
              currentPage: 1,
              pageSize: _defaultPageSize,
              hasMore: response.hasNextPage,
              total: response.total,
            ));
          }
        },
      );
    } on Exception catch (e) {
      emit(StudentReportListError('刷新报告列表失败: $e'));
    }
  }

  /// 处理加载更多学生报告列表事件
  Future<void> _onLoadMoreStudentReportList(
    LoadMoreStudentReportListEvent event,
    Emitter<StudentReportListState> emit,
  ) async {
    if (state is StudentReportListLoaded) {
      final currentState = state as StudentReportListLoaded;
      
      // 如果没有更多数据或正在加载更多，直接返回
      if (!currentState.hasMore || currentState.isLoadingMore) {
        return;
      }

      emit(StudentReportListLoadingMore(
        reports: currentState.reports,
        planId: event.planId,
        type: event.type,
        currentPage: currentState.currentPage,
        pageSize: currentState.pageSize,
        hasMore: currentState.hasMore,
        total: currentState.total,
      ));

      try {
        final nextPage = currentState.currentPage + 1;
        final result = await _getStudentReportList(
          GetStudentReportListParams(
            pageNum: nextPage,
            pageSize: _defaultPageSize,
            planId: event.planId,
            type: event.type,
          ),
        );

        result.fold(
          (failure) => emit(StudentReportListError(failure.message)),
          (response) {
            final allReports = [...currentState.reports, ...response.list];
            emit(StudentReportListLoaded(
              reports: allReports,
              planId: event.planId,
              type: event.type,
              currentPage: nextPage,
              pageSize: _defaultPageSize,
              hasMore: response.hasNextPage,
              total: response.total,
            ));
          },
        );
      } on Exception catch (e) {
        emit(StudentReportListError('加载更多报告失败: $e'));
      }
    }
  }

  /// 处理切换报告类型事件
  Future<void> _onChangeReportType(
    ChangeReportTypeEvent event,
    Emitter<StudentReportListState> emit,
  ) async {
    // 切换报告类型时重新加载数据
    add(LoadStudentReportListEvent(
      planId: event.planId,
      type: event.newType,
      reset: true,
    ));
  }

  /// 处理切换实习计划事件
  Future<void> _onChangePlan(
    ChangePlanEvent event,
    Emitter<StudentReportListState> emit,
  ) async {
    // 切换实习计划时重新加载数据
    add(LoadStudentReportListEvent(
      planId: event.newPlanId,
      type: event.type,
      reset: true,
    ));
  }
}

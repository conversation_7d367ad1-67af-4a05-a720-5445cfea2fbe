/// -----
/// student_report_list_repository.dart
/// 
/// 学生报告列表仓库接口
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';

/// 学生报告列表仓库接口
abstract class StudentReportListRepository {
  /// 获取学生报告列表
  ///
  /// [pageNum] - 当前页（从1开始）
  /// [pageSize] - 每页数量
  /// [planId] - 实习计划ID
  /// [type] - 报告类型（1:日报，2:周报，3:月报）
  Future<Either<Failure, StudentReportListResponse>> getStudentReportList({
    required int pageNum,
    required int pageSize,
    required String planId,
    required int type,
  });
}

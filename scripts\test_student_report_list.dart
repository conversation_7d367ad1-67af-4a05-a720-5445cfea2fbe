/// -----
/// test_student_report_list.dart
/// 
/// 测试学生报告列表API集成
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/di/injection_container.dart';
import 'package:flutter_demo/features/report/di/report_injection.dart';
import 'package:flutter_demo/features/report/domain/usecases/get_student_report_list.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化依赖注入
  await setupDependencies();
  await setupReportDependencies();
  
  print('开始测试学生报告列表API...');
  
  try {
    final getStudentReportList = GetIt.instance<GetStudentReportList>();
    
    // 测试参数
    final params = GetStudentReportListParams(
      pageNum: 1,
      pageSize: 10,
      planId: '2', // 使用默认的实习计划ID
      type: 1, // 周报
    );
    
    print('调用API参数:');
    print('- pageNum: ${params.pageNum}');
    print('- pageSize: ${params.pageSize}');
    print('- planId: ${params.planId}');
    print('- type: ${params.type}');
    
    final result = await getStudentReportList(params);
    
    result.fold(
      (failure) {
        print('❌ API调用失败: ${failure.message}');
      },
      (response) {
        print('✅ API调用成功!');
        print('响应数据:');
        print('- 总数据量: ${response.total}');
        print('- 当前页: ${response.pageNum}');
        print('- 每页数量: ${response.pageSize}');
        print('- 是否有下一页: ${response.hasNextPage}');
        print('- 报告列表数量: ${response.list.length}');
        
        if (response.list.isNotEmpty) {
          print('\n第一条报告数据:');
          final firstReport = response.list.first;
          print('- ID: ${firstReport.id}');
          print('- 学生姓名: ${firstReport.studentName}');
          print('- 企业名称: ${firstReport.companyName}');
          print('- 报告类型: ${firstReport.reportTypeDisplayText}');
          print('- 报告标题: ${firstReport.reportTitle}');
          print('- 状态: ${firstReport.isReviewed ? "已批阅" : "待批阅"}');
          print('- 创建时间: ${firstReport.createDateTime}');
          print('- 报告内容预览: ${firstReport.reportContent.length > 50 ? firstReport.reportContent.substring(0, 50) + "..." : firstReport.reportContent}');
        }
      },
    );
  } catch (e) {
    print('❌ 测试过程中发生异常: $e');
  }
  
  print('\n测试完成!');
}

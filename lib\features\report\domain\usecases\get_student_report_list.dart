/// -----
/// get_student_report_list.dart
/// 
/// 获取学生报告列表用例
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';
import 'package:flutter_demo/features/report/domain/repositories/student_report_list_repository.dart';

/// 获取学生报告列表用例
class GetStudentReportList implements UseCase<StudentReportListResponse, GetStudentReportListParams> {
  final StudentReportListRepository _repository;

  GetStudentReportList(this._repository);

  @override
  Future<Either<Failure, StudentReportListResponse>> call(GetStudentReportListParams params) async {
    return await _repository.getStudentReportList(
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      planId: params.planId,
      type: params.type,
    );
  }
}

/// 获取学生报告列表参数
class GetStudentReportListParams extends Equatable {
  /// 当前页（从1开始）
  final int pageNum;
  
  /// 每页数量
  final int pageSize;
  
  /// 实习计划ID
  final String planId;
  
  /// 报告类型（0:日报，1:周报，2:月报）
  final int type;

  const GetStudentReportListParams({
    required this.pageNum,
    required this.pageSize,
    required this.planId,
    required this.type,
  });

  @override
  List<Object?> get props => [pageNum, pageSize, planId, type];
}

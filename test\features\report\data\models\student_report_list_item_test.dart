/// -----
/// student_report_list_item_test.dart
/// 
/// 学生报告列表项模型测试
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';

void main() {
  group('StudentReportListItem', () {
    test('should parse JSO<PERSON> with string ID correctly', () {
      // arrange
      final json = {
        "id": "1938828963516981249",
        "studentId": "110",
        "studentName": "王小二",
        "avatar": null,
        "companyName": "掌淘网络科技（上海）有限公司",
        "jobDept": "客服",
        "jobName": "网络客服",
        "createTime": 1751087821237,
        "reportType": 1,
        "reportContent": "今天开发了实习日报接口联调",
        "reportDate": 1751040000000,
        "reportStartDate": 1751040000000,
        "reportEndDate": 1751040000000,
        "status": 0,
        "reviewComments": null,
        "reviewPerson": null,
        "companyStatus": 0,
        "companyReviewComments": null,
        "companyReviewPerson": null
      };

      // act
      final result = StudentReportListItem.fromJson(json);

      // assert
      expect(result.id, 1938828963516981249);
      expect(result.studentId, 110);
      expect(result.studentName, '王小二');
      expect(result.companyName, '掌淘网络科技（上海）有限公司');
      expect(result.reportType, 1);
      expect(result.status, 0);
    });

    test('should parse JSON with int ID correctly', () {
      // arrange
      final json = {
        "id": *********,
        "studentId": 110,
        "studentName": "王小二",
        "companyName": "测试公司",
        "createTime": 1751087821237,
        "reportType": 1,
        "reportContent": "测试内容",
        "reportDate": 1751040000000,
        "status": 0,
        "companyStatus": 0,
      };

      // act
      final result = StudentReportListItem.fromJson(json);

      // assert
      expect(result.id, *********);
      expect(result.studentId, 110);
    });

    test('should handle null values correctly', () {
      // arrange
      final json = {
        "id": "123",
        "studentId": "110",
        "studentName": "王小二",
        "companyName": "测试公司",
        "createTime": 1751087821237,
        "reportType": 1,
        "reportContent": "测试内容",
        "reportDate": 1751040000000,
        "status": 0,
        "companyStatus": 0,
        "avatar": null,
        "reviewComments": null,
        "reportStartDate": null,
        "reportEndDate": null,
      };

      // act
      final result = StudentReportListItem.fromJson(json);

      // assert
      expect(result.avatar, null);
      expect(result.reviewComments, null);
      expect(result.reportStartDate, null);
      expect(result.reportEndDate, null);
    });
  });

  group('StudentReportListResponse', () {
    test('should parse complete response JSON correctly', () {
      // arrange
      final json = {
        "total": 4,
        "list": [
          {
            "id": "1938828963516981249",
            "studentId": "110",
            "studentName": "王小二",
            "avatar": null,
            "companyName": "掌淘网络科技（上海）有限公司",
            "jobDept": "客服",
            "jobName": "网络客服",
            "createTime": 1751087821237,
            "reportType": 1,
            "reportContent": "今天开发了实习日报接口联调",
            "reportDate": 1751040000000,
            "reportStartDate": 1751040000000,
            "reportEndDate": 1751040000000,
            "status": 0,
            "reviewComments": null,
            "reviewPerson": null,
            "companyStatus": 0,
            "companyReviewComments": null,
            "companyReviewPerson": null
          }
        ],
        "pageNum": 1,
        "pageSize": 4,
        "size": 4,
        "startRow": 0,
        "endRow": 3,
        "pages": 1,
        "prePage": 0,
        "nextPage": 0,
        "isFirstPage": true,
        "isLastPage": true,
        "hasPreviousPage": false,
        "hasNextPage": false,
        "navigatePages": 8,
        "navigatepageNums": [1],
        "navigateFirstPage": 1,
        "navigateLastPage": 1
      };

      // act
      final result = StudentReportListResponse.fromJson(json);

      // assert
      expect(result.total, 4);
      expect(result.list.length, 1);
      expect(result.list.first.id, 1938828963516981249);
      expect(result.pageNum, 1);
      expect(result.hasNextPage, false);
    });
  });
}

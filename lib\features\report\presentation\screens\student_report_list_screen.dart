/// -----
/// student_report_list_screen.dart
///
/// 学生报告列表视图组件，用于显示日报/周报列表，支持展开/收起功能
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/features/report/presentation/bloc/student_list/student_report_list_bloc.dart';
import 'package:flutter_demo/features/report/presentation/bloc/student_list/student_report_list_event.dart';
import 'package:flutter_demo/features/report/presentation/bloc/student_list/student_report_list_state.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class StudentReportListView extends StatefulWidget {
  final ReportType type;

  const StudentReportListView({
    Key? key,
    required this.type,
  }) : super(key: key);

  @override
  State<StudentReportListView> createState() => _StudentReportListViewState();
}

class _StudentReportListViewState extends State<StudentReportListView> {
  late StudentReportListBloc _bloc;
  late ScrollController _scrollController;

  // 用于跟踪每个报告项的展开状态
  final Map<int, bool> _expandedStates = {};

  @override
  void initState() {
    super.initState();
    _bloc = GetIt.instance<StudentReportListBloc>();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    // 监听全局计划状态变化
    _loadInitialData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// 加载初始数据
  void _loadInitialData() {
    final planListBloc = GetIt.instance<PlanListGlobalBloc>();
    final planState = planListBloc.state;

    if (planState is PlanListGlobalLoadedState && planState.currentPlanId != null) {
      _bloc.add(LoadStudentReportListEvent(
        planId: planState.currentPlanId!,
        type: _getReportTypeValue(widget.type),
      ));
    }
  }

  /// 滚动监听，实现分页加载
  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      final state = _bloc.state;
      if (state is StudentReportListLoaded && state.hasMore && !state.isLoadingMore) {
        _bloc.add(LoadMoreStudentReportListEvent(
          planId: state.planId,
          type: state.type,
        ));
      }
    }
  }

  /// 获取报告类型对应的数值
  int _getReportTypeValue(ReportType reportType) {
    switch (reportType) {
      case ReportType.daily:
        return 0;
      case ReportType.weekly:
        return 1;
      case ReportType.monthly:
        return 2;
      case ReportType.summary:
        return 3;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: Scaffold(
        backgroundColor: Colors.grey[100],
        appBar: CustomAppBar(
          title: '我的报告',
          backgroundColor: Colors.transparent,
          elevation: 0,
          showBackButton: true,
        ),
        body: Column(
          children: [
            // 课程头部选择器
            BlocListener<PlanListGlobalBloc, PlanListGlobalState>(
              bloc: GetIt.instance<PlanListGlobalBloc>(),
              listener: (context, state) {
                if (state is PlanListGlobalLoadedState && state.currentPlanId != null) {
                  // 当计划变更时，重新加载报告列表
                  _bloc.add(ChangePlanEvent(
                    newPlanId: state.currentPlanId!,
                    type: _getReportTypeValue(widget.type),
                  ));
                }
              },
              child: const CourseHeaderSection(),
            ),

            // 报告列表
            Expanded(
              child: BlocBuilder<StudentReportListBloc, StudentReportListState>(
                builder: (context, state) {
                  if (state is StudentReportListLoading) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (state is StudentReportListError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            state.message,
                            style: TextStyle(
                              fontSize: 28.sp,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 20.h),
                          ElevatedButton(
                            onPressed: _loadInitialData,
                            child: const Text('重试'),
                          ),
                        ],
                      ),
                    );
                  } else if (state is StudentReportListEmpty) {
                    return Center(
                      child: Text(
                        '暂无报告数据',
                        style: TextStyle(
                          fontSize: 28.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    );
                  } else if (state is StudentReportListLoaded) {
                    return RefreshIndicator(
                      onRefresh: () async {
                        _bloc.add(RefreshStudentReportListEvent(
                          planId: state.planId,
                          type: state.type,
                        ));
                      },
                      child: ListView.builder(
                        controller: _scrollController,
                        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
                        itemCount: state.reports.length + (state.hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index < state.reports.length) {
                            final report = state.reports[index];
                            return _buildReportItem(context, report, index);
                          } else {
                            // 加载更多指示器
                            return Container(
                              padding: EdgeInsets.all(20.h),
                              alignment: Alignment.center,
                              child: state.isLoadingMore
                                  ? const CircularProgressIndicator()
                                  : const SizedBox.shrink(),
                            );
                          }
                        },
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建报告项
  Widget _buildReportItem(BuildContext context, StudentReportListItem report, int index) {
    final isExpanded = _expandedStates[report.id] ?? false;

    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 学生信息头部
          _buildStudentHeader(report),

          // 周报信息
          _buildReportInfo(report),

          // 问题列表
          _buildQuestionsList(report, isExpanded),

          // 底部信息
          _buildBottomInfo(report),
        ],
      ),
    );
  }

  // 构建学生信息头部
  Widget _buildStudentHeader(StudentReportListItem report) {
    return Padding(
      padding: EdgeInsets.fromLTRB(30.w, 30.h, 30.w, 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 学生头像
          CircleAvatar(
            radius: 44.r,
            backgroundColor: Colors.grey[200],
            backgroundImage: report.avatar != null && report.avatar!.isNotEmpty
                ? NetworkImage(report.avatar!)
                : const NetworkImage(AppConstants.avatar1),
          ),
          SizedBox(width: 12.w),

          // 学生信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      report.studentName,
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    // 实习生标签
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4.r),
                        border: Border.all(color: AppTheme.blue2165f6, width: 0.5),
                      ),
                      child: Text(
                        '实习生',
                        style: TextStyle(
                          fontSize: 20.sp,
                          color: AppTheme.blue2165f6,
                        ),
                      ),
                    ),
                    const Spacer(),
                    // 批阅状态
                    Text(
                      report.isReviewed ? '已批阅' : '待批阅',
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: report.isReviewed ? const Color(0xFF999999) : AppTheme.blue2165f6,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                // 公司名称
                Text(
                  report.companyName,
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建周报信息
  Widget _buildReportInfo(StudentReportListItem report) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0),
      child: Text(
        report.reportTitle,
        style: TextStyle(
          fontSize: 28.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // 构建问题列表
  Widget _buildQuestionsList(StudentReportListItem report, bool isExpanded) {
    final questions = _getQuestions(report);

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 根据展开状态显示不同内容
          if (!isExpanded) ...[
            // 收起状态：只显示问题标题列表
            _buildCollapsedQuestions(questions),
          ] else ...[
            // 展开状态：显示问题和答案
            _buildExpandedQuestions(questions, report),
            // 展开状态下显示批阅状态
            _buildReviewSection(report),
          ],
        ],
      ),
    );
  }

  // 构建收起状态的问题列表（只显示问题标题）
  Widget _buildCollapsedQuestions(List<Map<String, String>> questions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: questions.asMap().entries.map((entry) {
        final index = entry.key + 1;
        final question = entry.value;
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: Text(
            '$index.${question['question']}',
            style: TextStyle(
              fontSize: 24.sp,
              color: const Color(0xFF333333),
              height: 1.4,
            ),
          ),
        );
      }).toList(),
    );
  }

  // 构建展开状态的问题列表（显示问题和统一答案）
  Widget _buildExpandedQuestions(List<Map<String, String>> questions, StudentReportListItem report) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 20.h),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 显示所有问题
          ...questions.asMap().entries.map((entry) {
            final index = entry.key + 1;
            final question = entry.value;
            return Padding(
              padding: EdgeInsets.only(bottom: 12.h),
              child: Text(
                '$index.${question['question']}',
                style: TextStyle(
                  fontSize: 24.sp,
                  color: AppTheme.black333,
                  height: 1.4,
                ),
              ),
            );
          }).toList(),

          SizedBox(height: 16.h),

          // 报告内容
          Text(
            report.reportContent,
            style: TextStyle(
              fontSize: 24.sp,
              color: AppTheme.black333,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  // 构建批阅状态区域
  Widget _buildReviewSection(StudentReportListItem report) {
    final isReviewed = report.isReviewed;

    return Column(
      children: [
        SizedBox(height: 20.h),
        if (!isReviewed) ...[
          // 待批阅状态 - 居中显示
          Center(
            child: Column(
              children: [
                Text(
                  '暂无批阅～',
                  style: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  '请耐心等待老师的批阅',
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ] else ...[
          // 已批阅状态
          _buildReviewContent(report),
        ],
      ],
    );
  }

  // 构建已批阅内容
  Widget _buildReviewContent(StudentReportListItem report) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 评星
        Row(
          children: [
            Text(
              '评星',
              style: TextStyle(
                fontSize: 24.sp,
                color: Colors.black87,
              ),
            ),
            SizedBox(width: 16.w),
            Row(
              children: List.generate(5, (index) {
                return Icon(
                  index < 3 ? Icons.star : (index == 3 ? Icons.star_half : Icons.star_border),
                  color: index < 4 ? Colors.amber : Colors.grey[300],
                  size: 32.sp,
                );
              }),
            ),
          ],
        ),
        SizedBox(height: 20.h),
        // 水平分割线
        Container(
          height: 1.h,
          color: Colors.grey[300],
        ),

        SizedBox(height: 20.h),

        // 校内老师评语
        Row(
          children: [
            Image.asset('assets/images/teacher_comment_icon.png', width: 28.w, height: 28.h),
            SizedBox(width: 8.w),
            Text(
              '校内老师评语',
              style: TextStyle(
                fontSize: 30.sp,
                color: const Color(0xFF333333),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),

        // 水平分割线
        Container(
          height: 1.h,
          color: Colors.grey[300],
        ),
        SizedBox(height: 25.h),

        // 老师信息和评语
        if (report.hasTeacherReview) ...[
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 老师头像和基本信息
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 44.r,
                    backgroundColor: Colors.grey[200],
                    backgroundImage: const NetworkImage(AppConstants.avatar2),
                  ),
                  SizedBox(width: 12.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        report.reviewPerson ?? '老师',
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        DateFormat('yyyy-MM-dd HH:mm').format(report.createDateTime),
                        style: TextStyle(
                          fontSize: 20.sp,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              // 评语内容
              Text(
                report.reviewComments ?? '',
                style: TextStyle(
                  fontSize: 24.sp,
                  color: Colors.black87,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  // 构建底部信息
  Widget _buildBottomInfo(StudentReportListItem report) {
    final isExpanded = _expandedStates[report.id] ?? false;

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 16.h),
      child: Row(
        children: [
          Text(
            '提交时间：${DateFormat('yyyy-MM-dd HH:mm').format(report.createDateTime)}',
            style: TextStyle(
              fontSize: 22.sp,
              color: Colors.grey[600],
            ),
          ),
          const Spacer(),
          // 展开/收起按钮
          GestureDetector(
            onTap: () {
              setState(() {
                _expandedStates[report.id] = !isExpanded;
              });
            },
            child: Row(
              children: [
                Text(
                  isExpanded ? '收起' : '展开',
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(width: 4.w),
                Icon(
                  isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: Colors.grey[600],
                  size: 24.sp,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 获取问题列表
  List<Map<String, String>> _getQuestions(StudentReportListItem report) {
    // 根据报告类型返回不同的问题
    switch (report.reportType) {
      case 1: // 日报
        return [
          {'question': '今天的主要工作是什么？'},
          {'question': '您在工作中有哪些思考？'},
          {'question': '您觉得那些可以继续优化？'},
          {'question': '您的收获是什么？'},
        ];
      case 2: // 周报
        return [
          {'question': '本周完成的主要工作内容？'},
          {'question': '本周工作中的收获和体会？'},
          {'question': '遇到的问题及解决方案？'},
          {'question': '下周工作计划？'},
        ];
      case 3: // 月报
        return [
          {'question': '本月完成的主要工作内容？'},
          {'question': '本月工作成果和收获？'},
          {'question': '存在的问题和改进建议？'},
          {'question': '下月工作计划和目标？'},
        ];
      default:
        return [
          {'question': '工作内容概述？'},
          {'question': '主要收获和体会？'},
          {'question': '存在的问题？'},
          {'question': '改进计划？'},
        ];
    }
  }
}

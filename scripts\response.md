报告列表
接口：/v1/internship/report/teacher/list
POST
入参:
```json
{
  "pageNum": 0,
  "pageSize": 0,
  "planId": 0,
  "reportDate": 0,
  "reportEndDate": 0,
  "reportStartDate": 0,
  "status": 0,
  "type": 0
}
```
入参说明:
{
    pageNum	integer($int32) 当前页
    pageSize	integer($int32) 每页数量
    planId	string 实习计划ID
    reportDate	integer(时间戳毫秒) 对应的日期（日报为具体日期，周报为起始日，月报为1号）
    reportEndDate	integer(时间戳毫秒) 结束日期（周报/月报使用）
    reportStartDate	integer(时间戳毫秒) 起始日期（周报/月报使用）
    status	integer($int32) 审批状态 0:待审批，1:已经审批
    type	integer($int32) 1:日报，2:周报，3:月报
}

返回结果：
```json
{
  "data": {
    "endRow": 0,
    "hasNextPage": true,
    "hasPreviousPage": true,
    "isFirstPage": true,
    "isLastPage": true,
    "list": [
      {
        "avatar": "string",
        "companyName": "string",
        "companyReviewComments": "string",
        "companyReviewPerson": "string",
        "companyStatus": 0,
        "createTime": 0,
        "id": 0,
        "jobDept": "string",
        "jobName": "string",
        "reportContent": "string",
        "reportDate": 0,
        "reportEndDate": 0,
        "reportStartDate": 0,
        "reportType": 0,
        "reviewComments": "string",
        "reviewPerson": "string",
        "status": 0,
        "studentId": 0,
        "studentName": "string"
      }
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "navigatePages": 0,
    "navigatepageNums": [
      0
    ],
    "nextPage": 0,
    "pageNum": 0,
    "pageSize": 0,
    "pages": 0,
    "prePage": 0,
    "size": 0,
    "startRow": 0,
    "total": 0
  },
  "resultCode": "string",
  "resultMsg": "string"
}
```

list字段的说明:
{
    avatar	string 头像
    companyName	string 企业名称
    companyReviewComments	string 指导老师审核意见
    companyReviewPerson	string 指导老师
    companyStatus	integer($int32) 指导老师状态（0=待批阅，1=已批阅）
    createTime	integer(时间戳) 创建时间
    id	integer($int64)报告ID
    jobDept	string 岗位部门
    jobName	string 岗位名称
    reportContent	string 报告内容
    reportDate	integer(时间戳) 对应的日期（日报为具体日期，周报为起始日，月报为1号）
    reportEndDate	integer(时间戳) 结束日期（周报/月报使用）
    reportStartDate	integer(时间戳) 起始日期（周报/月报使用）
    reportType	integer($int32) 报告类型（1=日报，2=周报，3=月报，4=总结）
    reviewComments	string 老师审核意见
    reviewPerson	string 审核人
    status	integer($int32) 状态（0=待批阅，1=已批阅）
    studentId	integer($int64) 学生ID
    studentName	string 学生姓名
}

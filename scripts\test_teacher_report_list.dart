/// -----
/// test_teacher_report_list.dart
/// 
/// 测试教师端报告列表API集成
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/config/injection/injection.dart';
import 'package:flutter_demo/features/report/domain/usecases/get_teacher_report_list.dart';
import 'package:get_it/get_it.dart';

void main() async {
  print('🚀 开始测试教师端报告列表API集成...');
  
  // 初始化依赖注入
  await setupInjection();
  
  try {
    final getTeacherReportList = GetIt.instance<GetTeacherReportList>();
    
    // 测试参数
    final params = GetTeacherReportListParams(
      pageNum: 1,
      pageSize: 10,
      planId: '2', // 使用默认的实习计划ID
      type: 1, // 周报
      status: 0, // 待审批
    );
    
    print('📋 调用API参数:');
    print('- pageNum: ${params.pageNum}');
    print('- pageSize: ${params.pageSize}');
    print('- planId: ${params.planId}');
    print('- type: ${params.type}');
    print('- status: ${params.status}');
    
    final result = await getTeacherReportList(params);
    
    result.fold(
      (failure) {
        print('❌ API调用失败: ${failure.message}');
      },
      (response) {
        print('✅ API调用成功!');
        print('📊 响应数据:');
        print('- 总数据量: ${response.total}');
        print('- 当前页: ${response.pageNum}');
        print('- 每页数量: ${response.pageSize}');
        print('- 是否有下一页: ${response.hasNextPage}');
        print('- 报告列表数量: ${response.list.length}');
        
        if (response.list.isNotEmpty) {
          print('\n📝 第一条报告信息:');
          final firstReport = response.list.first;
          print('- ID: ${firstReport.id}');
          print('- 学生姓名: ${firstReport.studentName}');
          print('- 企业名称: ${firstReport.companyName}');
          print('- 报告类型: ${firstReport.reportType}');
          print('- 状态: ${firstReport.status}');
          print('- 创建时间: ${DateTime.fromMillisecondsSinceEpoch(firstReport.createTime)}');
          print('- 报告内容: ${firstReport.reportContent.length > 50 ? firstReport.reportContent.substring(0, 50) + "..." : firstReport.reportContent}');
        }
      },
    );
  } catch (e) {
    print('💥 测试过程中发生异常: $e');
  }
  
  print('\n🏁 测试完成!');
}

/// -----
/// student_report_list_state.dart
/// 
/// 学生报告列表页面的状态定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';

abstract class StudentReportListState extends Equatable {
  const StudentReportListState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class StudentReportListInitial extends StudentReportListState {}

/// 加载中状态
class StudentReportListLoading extends StudentReportListState {}

/// 加载成功状态
class StudentReportListLoaded extends StudentReportListState {
  /// 报告列表
  final List<StudentReportListItem> reports;
  
  /// 当前实习计划ID
  final String planId;
  
  /// 当前报告类型（1:日报，2:周报，3:月报）
  final int type;
  
  /// 当前页码
  final int currentPage;
  
  /// 每页数量
  final int pageSize;
  
  /// 是否有更多数据
  final bool hasMore;
  
  /// 是否正在加载更多
  final bool isLoadingMore;
  
  /// 是否正在刷新
  final bool isRefreshing;
  
  /// 总数据量
  final int total;

  const StudentReportListLoaded({
    required this.reports,
    required this.planId,
    required this.type,
    required this.currentPage,
    required this.pageSize,
    required this.hasMore,
    this.isLoadingMore = false,
    this.isRefreshing = false,
    required this.total,
  });

  /// 复制状态并更新部分字段
  StudentReportListLoaded copyWith({
    List<StudentReportListItem>? reports,
    String? planId,
    int? type,
    int? currentPage,
    int? pageSize,
    bool? hasMore,
    bool? isLoadingMore,
    bool? isRefreshing,
    int? total,
  }) {
    return StudentReportListLoaded(
      reports: reports ?? this.reports,
      planId: planId ?? this.planId,
      type: type ?? this.type,
      currentPage: currentPage ?? this.currentPage,
      pageSize: pageSize ?? this.pageSize,
      hasMore: hasMore ?? this.hasMore,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      total: total ?? this.total,
    );
  }

  @override
  List<Object?> get props => [
        reports,
        planId,
        type,
        currentPage,
        pageSize,
        hasMore,
        isLoadingMore,
        isRefreshing,
        total,
      ];
}

/// 刷新中状态
class StudentReportListRefreshing extends StudentReportListLoaded {
  const StudentReportListRefreshing({
    required super.reports,
    required super.planId,
    required super.type,
    required super.currentPage,
    required super.pageSize,
    required super.hasMore,
    required super.total,
  }) : super(isRefreshing: true);
}

/// 加载更多中状态
class StudentReportListLoadingMore extends StudentReportListLoaded {
  const StudentReportListLoadingMore({
    required super.reports,
    required super.planId,
    required super.type,
    required super.currentPage,
    required super.pageSize,
    required super.hasMore,
    required super.total,
  }) : super(isLoadingMore: true);
}

/// 加载失败状态
class StudentReportListError extends StudentReportListState {
  final String message;

  const StudentReportListError(this.message);

  @override
  List<Object?> get props => [message];
}

/// 空数据状态
class StudentReportListEmpty extends StudentReportListState {
  /// 当前实习计划ID
  final String planId;
  
  /// 当前报告类型（1:日报，2:周报，3:月报）
  final int type;

  const StudentReportListEmpty({
    required this.planId,
    required this.type,
  });

  @override
  List<Object?> get props => [planId, type];
}

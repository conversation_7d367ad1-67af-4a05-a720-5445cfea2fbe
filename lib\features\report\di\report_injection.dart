/// -----
/// report_injection.dart
/// 
/// 报告模块依赖注入配置
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import '../../../core/network/dio_client.dart';
import '../../../core/network/network_info.dart';
import '../../../core/utils/file_upload_util.dart';
import '../data/datasources/remote/report_tip_remote_data_source.dart';
import '../data/datasources/remote/student_report_list_remote_data_source.dart';
import '../data/repositories/report_tip_repository_impl.dart';
import '../data/repositories/student_report_list_repository_impl.dart';
import '../domain/repositories/report_tip_repository.dart';
import '../domain/repositories/student_report_list_repository.dart';
import '../domain/usecases/get_report_tip.dart';
import '../domain/usecases/get_student_report_list.dart';
import '../presentation/bloc/write/report_write_bloc.dart';
import '../presentation/bloc/student_list/student_report_list_bloc.dart';

/// 全局依赖注入容器
final getIt = GetIt.instance;

/// 初始化报告模块依赖
///
/// 注册报告模块的数据源、仓库、用例和BLoC
Future<void> setupReportDependencies() async {
  // 注意：FileUploadUtil 已在核心依赖中注册，这里不需要重复注册

  // 数据源
  getIt.registerLazySingleton<ReportTipRemoteDataSource>(
    () => ReportTipRemoteDataSourceImpl(getIt<DioClient>()),
  );

  getIt.registerLazySingleton<StudentReportListRemoteDataSource>(
    () => StudentReportListRemoteDataSourceImpl(getIt<DioClient>()),
  );

  // 仓库
  getIt.registerLazySingleton<ReportTipRepository>(
    () => ReportTipRepositoryImpl(getIt<ReportTipRemoteDataSource>()),
  );

  getIt.registerLazySingleton<StudentReportListRepository>(
    () => StudentReportListRepositoryImpl(
      getIt<StudentReportListRemoteDataSource>(),
      getIt<NetworkInfo>(),
    ),
  );

  // 用例
  getIt.registerFactory<GetReportTip>(
    () => GetReportTip(getIt<ReportTipRepository>()),
  );

  getIt.registerFactory<GetStudentReportList>(
    () => GetStudentReportList(getIt<StudentReportListRepository>()),
  );

  // BLoC - 使用工厂模式，每次使用时创建新实例
  getIt.registerFactory<ReportWriteBloc>(
    () => ReportWriteBloc(
      getIt<GetReportTip>(),
      getIt<FileUploadUtil>(),
    ),
  );

  getIt.registerFactory<StudentReportListBloc>(
    () => StudentReportListBloc(getIt<GetStudentReportList>()),
  );
}

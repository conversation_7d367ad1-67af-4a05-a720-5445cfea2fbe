/// -----
/// get_teacher_report_list.dart
/// 
/// 获取教师端报告列表用例
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';
import 'package:flutter_demo/features/report/domain/repositories/teacher_report_list_repository.dart';

/// 获取教师端报告列表用例
class GetTeacherReportList implements UseCase<StudentReportListResponse, GetTeacherReportListParams> {
  final TeacherReportListRepository _repository;

  GetTeacherReportList(this._repository);

  @override
  Future<Either<Failure, StudentReportListResponse>> call(GetTeacherReportListParams params) async {
    return await _repository.getTeacherReportList(
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      planId: params.planId,
      reportDate: params.reportDate,
      reportEndDate: params.reportEndDate,
      reportStartDate: params.reportStartDate,
      status: params.status,
      type: params.type,
    );
  }
}

/// 获取教师端报告列表参数
class GetTeacherReportListParams extends Equatable {
  /// 当前页（从1开始）
  final int pageNum;
  
  /// 每页数量
  final int pageSize;
  
  /// 实习计划ID
  final String planId;
  
  /// 对应的日期（日报为具体日期，周报为起始日，月报为1号）
  final int? reportDate;
  
  /// 结束日期（周报/月报使用）
  final int? reportEndDate;
  
  /// 起始日期（周报/月报使用）
  final int? reportStartDate;
  
  /// 审批状态 0:待审批，1:已经审批
  final int? status;
  
  /// 报告类型（0:日报，1:周报，2:月报）
  final int type;

  const GetTeacherReportListParams({
    required this.pageNum,
    required this.pageSize,
    required this.planId,
    this.reportDate,
    this.reportEndDate,
    this.reportStartDate,
    this.status,
    required this.type,
  });

  @override
  List<Object?> get props => [
        pageNum,
        pageSize,
        planId,
        reportDate,
        reportEndDate,
        reportStartDate,
        status,
        type,
      ];
}

/// -----
/// student_report_list_repository_impl.dart
/// 
/// 学生报告列表仓库实现
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/features/report/data/datasources/remote/student_report_list_remote_data_source.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';
import 'package:flutter_demo/features/report/domain/repositories/student_report_list_repository.dart';

/// 学生报告列表仓库实现
class StudentReportListRepositoryImpl implements StudentReportListRepository {
  final StudentReportListRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;

  StudentReportListRepositoryImpl(
    this._remoteDataSource,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, StudentReportListResponse>> getStudentReportList({
    required int pageNum,
    required int pageSize,
    required String planId,
    required int type,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final response = await _remoteDataSource.getStudentReportList(
          pageNum: pageNum,
          pageSize: pageSize,
          planId: planId,
          type: type,
        );
        return Right(response);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('获取学生报告列表失败: $e'));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }
}

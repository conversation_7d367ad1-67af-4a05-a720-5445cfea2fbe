/// -----
/// teacher_report_list_repository.dart
/// 
/// 教师端报告列表仓库接口
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';

/// 教师端报告列表仓库接口
abstract class TeacherReportListRepository {
  /// 获取教师端报告列表
  ///
  /// [pageNum] - 当前页（从1开始）
  /// [pageSize] - 每页数量
  /// [planId] - 实习计划ID
  /// [reportDate] - 对应的日期（日报为具体日期，周报为起始日，月报为1号）
  /// [reportEndDate] - 结束日期（周报/月报使用）
  /// [reportStartDate] - 起始日期（周报/月报使用）
  /// [status] - 审批状态 0:待审批，1:已经审批
  /// [type] - 报告类型（0:日报，1:周报，2:月报）
  Future<Either<Failure, StudentReportListResponse>> getTeacherReportList({
    required int pageNum,
    required int pageSize,
    required String planId,
    int? reportDate,
    int? reportEndDate,
    int? reportStartDate,
    int? status,
    required int type,
  });
}

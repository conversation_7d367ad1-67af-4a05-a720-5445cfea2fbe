/// -----
/// teacher_report_list_state.dart
/// 
/// 教师端报告列表状态
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';

/// 教师端报告列表状态基类
abstract class TeacherReportListState extends Equatable {
  const TeacherReportListState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class TeacherReportListInitial extends TeacherReportListState {}

/// 加载中状态
class TeacherReportListLoading extends TeacherReportListState {}

/// 加载成功状态
class TeacherReportListLoaded extends TeacherReportListState {
  /// 报告列表
  final List<StudentReportListItem> reports;
  
  /// 实习计划ID
  final String planId;
  
  /// 报告类型（1:日报，2:周报，3:月报）
  final int type;
  
  /// 审批状态 0:待审批，1:已经审批
  final int? status;
  
  /// 当前页码
  final int currentPage;
  
  /// 每页数量
  final int pageSize;
  
  /// 是否有更多数据
  final bool hasMore;
  
  /// 总数据量
  final int total;
  
  /// 是否正在加载更多
  final bool isLoadingMore;

  const TeacherReportListLoaded({
    required this.reports,
    required this.planId,
    required this.type,
    this.status,
    required this.currentPage,
    required this.pageSize,
    required this.hasMore,
    required this.total,
    this.isLoadingMore = false,
  });

  /// 复制状态并更新部分字段
  TeacherReportListLoaded copyWith({
    List<StudentReportListItem>? reports,
    String? planId,
    int? type,
    int? status,
    int? currentPage,
    int? pageSize,
    bool? hasMore,
    int? total,
    bool? isLoadingMore,
  }) {
    return TeacherReportListLoaded(
      reports: reports ?? this.reports,
      planId: planId ?? this.planId,
      type: type ?? this.type,
      status: status ?? this.status,
      currentPage: currentPage ?? this.currentPage,
      pageSize: pageSize ?? this.pageSize,
      hasMore: hasMore ?? this.hasMore,
      total: total ?? this.total,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }

  @override
  List<Object?> get props => [
        reports,
        planId,
        type,
        status,
        currentPage,
        pageSize,
        hasMore,
        total,
        isLoadingMore,
      ];
}

/// 空数据状态
class TeacherReportListEmpty extends TeacherReportListState {
  /// 实习计划ID
  final String planId;
  
  /// 报告类型（1:日报，2:周报，3:月报）
  final int type;
  
  /// 审批状态 0:待审批，1:已经审批
  final int? status;

  const TeacherReportListEmpty({
    required this.planId,
    required this.type,
    this.status,
  });

  @override
  List<Object?> get props => [planId, type, status];
}

/// 错误状态
class TeacherReportListError extends TeacherReportListState {
  /// 错误信息
  final String message;

  const TeacherReportListError(this.message);

  @override
  List<Object?> get props => [message];
}

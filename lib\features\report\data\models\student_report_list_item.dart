/// -----
/// student_report_list_item.dart
/// 
/// 学生报告列表项数据模型
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';

/// 学生报告列表项模型
class StudentReportListItem extends Equatable {
  /// 头像
  final String? avatar;
  
  /// 企业名称
  final String companyName;
  
  /// 指导老师审核意见
  final String? companyReviewComments;
  
  /// 指导老师
  final String? companyReviewPerson;
  
  /// 指导老师状态（0=待批阅，1=已批阅）
  final int companyStatus;
  
  /// 创建时间（时间戳）
  final int createTime;
  
  /// 报告ID
  final int id;
  
  /// 岗位部门
  final String? jobDept;
  
  /// 岗位名称
  final String? jobName;
  
  /// 报告内容
  final String reportContent;
  
  /// 对应的日期（时间戳）
  final int reportDate;
  
  /// 结束日期（时间戳，周报/月报使用）
  final int? reportEndDate;
  
  /// 起始日期（时间戳，周报/月报使用）
  final int? reportStartDate;
  
  /// 报告类型（1=日报，2=周报，3=月报，4=总结）
  final int reportType;
  
  /// 老师审核意见
  final String? reviewComments;
  
  /// 审核人
  final String? reviewPerson;
  
  /// 状态（0=待批阅，1=已批阅）
  final int status;
  
  /// 学生ID
  final int studentId;
  
  /// 学生姓名
  final String studentName;

  const StudentReportListItem({
    this.avatar,
    required this.companyName,
    this.companyReviewComments,
    this.companyReviewPerson,
    required this.companyStatus,
    required this.createTime,
    required this.id,
    this.jobDept,
    this.jobName,
    required this.reportContent,
    required this.reportDate,
    this.reportEndDate,
    this.reportStartDate,
    required this.reportType,
    this.reviewComments,
    this.reviewPerson,
    required this.status,
    required this.studentId,
    required this.studentName,
  });

  /// 从JSON创建实例
  factory StudentReportListItem.fromJson(Map<String, dynamic> json) {
    return StudentReportListItem(
      avatar: json['avatar'] as String?,
      companyName: json['companyName'] as String? ?? '',
      companyReviewComments: json['companyReviewComments'] as String?,
      companyReviewPerson: json['companyReviewPerson'] as String?,
      companyStatus: json['companyStatus'] as int? ?? 0,
      createTime: json['createTime'] as int? ?? 0,
      id: json['id'] as int? ?? 0,
      jobDept: json['jobDept'] as String?,
      jobName: json['jobName'] as String?,
      reportContent: json['reportContent'] as String? ?? '',
      reportDate: json['reportDate'] as int? ?? 0,
      reportEndDate: json['reportEndDate'] as int?,
      reportStartDate: json['reportStartDate'] as int?,
      reportType: json['reportType'] as int? ?? 1,
      reviewComments: json['reviewComments'] as String?,
      reviewPerson: json['reviewPerson'] as String?,
      status: json['status'] as int? ?? 0,
      studentId: json['studentId'] as int? ?? 0,
      studentName: json['studentName'] as String? ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'avatar': avatar,
      'companyName': companyName,
      'companyReviewComments': companyReviewComments,
      'companyReviewPerson': companyReviewPerson,
      'companyStatus': companyStatus,
      'createTime': createTime,
      'id': id,
      'jobDept': jobDept,
      'jobName': jobName,
      'reportContent': reportContent,
      'reportDate': reportDate,
      'reportEndDate': reportEndDate,
      'reportStartDate': reportStartDate,
      'reportType': reportType,
      'reviewComments': reviewComments,
      'reviewPerson': reviewPerson,
      'status': status,
      'studentId': studentId,
      'studentName': studentName,
    };
  }

  /// 获取报告状态枚举
  ReportStatus get reportStatus {
    switch (status) {
      case 0:
        return ReportStatus.submitted; // 待批阅
      case 1:
        return ReportStatus.approved; // 已批阅
      default:
        return ReportStatus.submitted;
    }
  }

  /// 获取报告类型枚举
  ReportType get reportTypeEnum {
    switch (reportType) {
      case 1:
        return ReportType.daily;
      case 2:
        return ReportType.weekly;
      case 3:
        return ReportType.monthly;
      case 4:
        return ReportType.summary;
      default:
        return ReportType.daily;
    }
  }

  /// 获取创建时间的DateTime对象
  DateTime get createDateTime {
    return DateTime.fromMillisecondsSinceEpoch(createTime);
  }

  /// 获取报告日期的DateTime对象
  DateTime get reportDateTime {
    return DateTime.fromMillisecondsSinceEpoch(reportDate);
  }

  /// 获取报告起始日期的DateTime对象（如果有）
  DateTime? get reportStartDateTime {
    return reportStartDate != null 
        ? DateTime.fromMillisecondsSinceEpoch(reportStartDate!)
        : null;
  }

  /// 获取报告结束日期的DateTime对象（如果有）
  DateTime? get reportEndDateTime {
    return reportEndDate != null 
        ? DateTime.fromMillisecondsSinceEpoch(reportEndDate!)
        : null;
  }

  /// 获取报告类型显示文本
  String get reportTypeDisplayText {
    switch (reportType) {
      case 1:
        return '日报';
      case 2:
        return '周报';
      case 3:
        return '月报';
      case 4:
        return '总结';
      default:
        return '报告';
    }
  }

  /// 获取报告标题（根据类型和日期生成）
  String get reportTitle {
    final dateTime = reportDateTime;
    switch (reportType) {
      case 1: // 日报
        return '${dateTime.month}.${dateTime.day.toString().padLeft(2, '0')}日报';
      case 2: // 周报
        if (reportStartDateTime != null && reportEndDateTime != null) {
          final start = reportStartDateTime!;
          final end = reportEndDateTime!;
          return '周报 ${start.year}.${start.month.toString().padLeft(2, '0')}.${start.day.toString().padLeft(2, '0')}-${end.year}.${end.month.toString().padLeft(2, '0')}.${end.day.toString().padLeft(2, '0')}';
        }
        return '周报';
      case 3: // 月报
        return '${dateTime.year}年${dateTime.month}月月报';
      case 4: // 总结
        return '实习总结';
      default:
        return '报告';
    }
  }

  /// 是否已批阅
  bool get isReviewed => status == 1;

  /// 是否有老师评语
  bool get hasTeacherReview => reviewComments != null && reviewComments!.isNotEmpty;

  /// 是否有企业指导老师评语
  bool get hasCompanyReview => companyReviewComments != null && companyReviewComments!.isNotEmpty;

  @override
  List<Object?> get props => [
        avatar,
        companyName,
        companyReviewComments,
        companyReviewPerson,
        companyStatus,
        createTime,
        id,
        jobDept,
        jobName,
        reportContent,
        reportDate,
        reportEndDate,
        reportStartDate,
        reportType,
        reviewComments,
        reviewPerson,
        status,
        studentId,
        studentName,
      ];
}

/// 学生报告列表响应模型
class StudentReportListResponse extends Equatable {
  /// 结束行
  final int endRow;
  
  /// 是否有下一页
  final bool hasNextPage;
  
  /// 是否有上一页
  final bool hasPreviousPage;
  
  /// 是否是第一页
  final bool isFirstPage;
  
  /// 是否是最后一页
  final bool isLastPage;
  
  /// 报告列表
  final List<StudentReportListItem> list;
  
  /// 导航第一页
  final int navigateFirstPage;
  
  /// 导航最后一页
  final int navigateLastPage;
  
  /// 导航页数
  final int navigatePages;
  
  /// 导航页码数组
  final List<int> navigatepageNums;
  
  /// 下一页
  final int nextPage;
  
  /// 当前页
  final int pageNum;
  
  /// 每页数量
  final int pageSize;
  
  /// 总页数
  final int pages;
  
  /// 上一页
  final int prePage;
  
  /// 当前页数据量
  final int size;
  
  /// 开始行
  final int startRow;
  
  /// 总数据量
  final int total;

  const StudentReportListResponse({
    required this.endRow,
    required this.hasNextPage,
    required this.hasPreviousPage,
    required this.isFirstPage,
    required this.isLastPage,
    required this.list,
    required this.navigateFirstPage,
    required this.navigateLastPage,
    required this.navigatePages,
    required this.navigatepageNums,
    required this.nextPage,
    required this.pageNum,
    required this.pageSize,
    required this.pages,
    required this.prePage,
    required this.size,
    required this.startRow,
    required this.total,
  });

  /// 从JSON创建实例
  factory StudentReportListResponse.fromJson(Map<String, dynamic> json) {
    return StudentReportListResponse(
      endRow: json['endRow'] as int? ?? 0,
      hasNextPage: json['hasNextPage'] as bool? ?? false,
      hasPreviousPage: json['hasPreviousPage'] as bool? ?? false,
      isFirstPage: json['isFirstPage'] as bool? ?? true,
      isLastPage: json['isLastPage'] as bool? ?? true,
      list: (json['list'] as List<dynamic>?)
              ?.map((item) => StudentReportListItem.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      navigateFirstPage: json['navigateFirstPage'] as int? ?? 1,
      navigateLastPage: json['navigateLastPage'] as int? ?? 1,
      navigatePages: json['navigatePages'] as int? ?? 1,
      navigatepageNums: (json['navigatepageNums'] as List<dynamic>?)
              ?.map((item) => item as int)
              .toList() ??
          [],
      nextPage: json['nextPage'] as int? ?? 0,
      pageNum: json['pageNum'] as int? ?? 1,
      pageSize: json['pageSize'] as int? ?? 10,
      pages: json['pages'] as int? ?? 1,
      prePage: json['prePage'] as int? ?? 0,
      size: json['size'] as int? ?? 0,
      startRow: json['startRow'] as int? ?? 0,
      total: json['total'] as int? ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        endRow,
        hasNextPage,
        hasPreviousPage,
        isFirstPage,
        isLastPage,
        list,
        navigateFirstPage,
        navigateLastPage,
        navigatePages,
        navigatepageNums,
        nextPage,
        pageNum,
        pageSize,
        pages,
        prePage,
        size,
        startRow,
        total,
      ];
}

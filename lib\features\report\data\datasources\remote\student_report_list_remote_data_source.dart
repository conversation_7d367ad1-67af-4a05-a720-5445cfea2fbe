/// -----
/// student_report_list_remote_data_source.dart
/// 
/// 学生报告列表远程数据源
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';

/// 学生报告列表远程数据源抽象接口
abstract class StudentReportListRemoteDataSource {
  /// 获取学生报告列表
  ///
  /// [pageNum] - 当前页（从1开始）
  /// [pageSize] - 每页数量
  /// [planId] - 实习计划ID
  /// [type] - 报告类型（0:日报，1:周报，2:月报）
  Future<StudentReportListResponse> getStudentReportList({
    required int pageNum,
    required int pageSize,
    required String planId,
    required int type,
  });
}

/// 学生报告列表远程数据源实现
class StudentReportListRemoteDataSourceImpl implements StudentReportListRemoteDataSource {
  final DioClient _dioClient;

  StudentReportListRemoteDataSourceImpl(this._dioClient);

  @override
  Future<StudentReportListResponse> getStudentReportList({
    required int pageNum,
    required int pageSize,
    required String planId,
    required int type,
  }) async {
    try {
      final response = await _dioClient.post(
        'internshipservice/v1/internship/report/student/list',
        data: {
          'pageNum': pageNum,
          'pageSize': pageSize,
          'planId': planId,
          'type': type,
        },
      );

      // DioClient已经处理了响应格式，直接返回data字段
      if (response is Map<String, dynamic>) {
        return StudentReportListResponse.fromJson(response);
      } else {
        throw ServerException('获取学生报告列表响应格式错误');
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取学生报告列表失败: $e');
    }
  }
}

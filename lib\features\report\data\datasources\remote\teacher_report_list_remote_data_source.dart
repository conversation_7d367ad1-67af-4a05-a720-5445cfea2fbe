/// -----
/// teacher_report_list_remote_data_source.dart
/// 
/// 教师端报告列表远程数据源
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/features/report/data/models/student_report_list_item.dart';

/// 教师端报告列表远程数据源抽象接口
abstract class TeacherReportListRemoteDataSource {
  /// 获取教师端报告列表
  ///
  /// [pageNum] - 当前页（从1开始）
  /// [pageSize] - 每页数量
  /// [planId] - 实习计划ID
  /// [reportDate] - 对应的日期（日报为具体日期，周报为起始日，月报为1号）
  /// [reportEndDate] - 结束日期（周报/月报使用）
  /// [reportStartDate] - 起始日期（周报/月报使用）
  /// [status] - 审批状态 0:待审批，1:已经审批
  /// [type] - 报告类型（0:日报，1:周报，2:月报）
  Future<StudentReportListResponse> getTeacherReportList({
    required int pageNum,
    required int pageSize,
    required String planId,
    int? reportDate,
    int? reportEndDate,
    int? reportStartDate,
    int? status,
    required int type,
  });
}

/// 教师端报告列表远程数据源实现
class TeacherReportListRemoteDataSourceImpl implements TeacherReportListRemoteDataSource {
  final DioClient _dioClient;

  TeacherReportListRemoteDataSourceImpl(this._dioClient);

  @override
  Future<StudentReportListResponse> getTeacherReportList({
    required int pageNum,
    required int pageSize,
    required String planId,
    int? reportDate,
    int? reportEndDate,
    int? reportStartDate,
    int? status,
    required int type,
  }) async {
    try {
      final response = await _dioClient.post(
        'v1/internship/report/teacher/list',
        data: {
          'pageNum': pageNum,
          'pageSize': pageSize,
          'planId': planId,
          if (reportDate != null) 'reportDate': reportDate,
          if (reportEndDate != null) 'reportEndDate': reportEndDate,
          if (reportStartDate != null) 'reportStartDate': reportStartDate,
          if (status != null) 'status': status,
          'type': type,
        },
      );

      // DioClient已经处理了响应格式，直接返回data字段
      if (response is Map<String, dynamic>) {
        return StudentReportListResponse.fromJson(response);
      } else {
        throw ServerException('获取教师端报告列表响应格式错误');
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取教师端报告列表失败: $e');
    }
  }
}
